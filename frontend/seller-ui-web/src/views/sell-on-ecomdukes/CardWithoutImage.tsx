'use client';

import Link from 'next/link';
import { Box, Button, Container, Grid, Typography } from '@mui/material';
import Image from 'next/image';
import { SellContentSection } from 'types/sell-content';

import AnimateButton from 'components/@extended/AnimateButton';
import { convertToHtml } from 'utils/convertToHtml';

interface CardWithImageSectionProps {
  section: SellContentSection;
}

export default function CardWithoutImageSection({ section }: CardWithImageSectionProps) {
  const descriptionItem = section?.items?.find((item) => item.renderType === 'DESCRIPTION_ONLY');
  const buttonItems = section?.items?.filter((item) => item.renderType === 'BUTTON');

  const showAppIcons = descriptionItem?.metadata?.showAppIcons;
  const appIconType = descriptionItem?.metadata?.appIconType;

  return (
    <Box sx={{ display: 'flex', justifyContent: 'center', px: 2, py: 4 }}>
      <Box
        sx={{
          width: '100%',
          maxWidth: '1200px',
          bgcolor: '#EFF1F2',
          borderRadius: 3,
          boxShadow: 3,
          p: { xs: 3, sm: 5, md: 8, lg: 10 }
        }}
      >
        <Container disableGutters>
          <Grid container spacing={4} alignItems="center">
            <Grid item xs={12}>
              <Typography variant="h4" sx={{ color: '#00004F', fontWeight: 'bold', mb: 3 }}>
                {section.sectionTitle}
              </Typography>

              {descriptionItem?.description && (
                <Box
                  sx={{
                    '& h1, & h2, & h3, & h4, & h5, & h6, & p': {
                      fontWeight: 400,
                      lineHeight: 1.4,
                      margin: '8px 0'
                    }
                  }}
                  dangerouslySetInnerHTML={{ __html: convertToHtml(descriptionItem.description) }}
                />
              )}

              {/* Buttons (if any) + App Icons in one row */}
              <Box
                sx={{
                  mt: 4,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 25,
                  flexWrap: 'wrap',
                  justifyContent: { xs: 'center', sm: 'flex-start' }
                }}
              >
                {/* Render button(s) if available */}
                {(buttonItems?.length ?? 0) > 0 &&
                  buttonItems?.map((btn) => (
                    <AnimateButton key={btn.id}>
                      <Button
                        component={Link}
                        href={btn.metadata?.redirectUrl || '#'}
                        size="large"
                        color="primary"
                        variant="contained"
                        sx={{ fontWeight: 'bold', p: '15px', width: { xs: '100%', sm: '80%', md: '150%' } }}
                      >
                        {btn.title || 'Click Here'}
                      </Button>
                    </AnimateButton>
                  ))}

                {/* App Icons always render if enabled */}
                {showAppIcons &&
                  (appIconType === 'IMAGE' ? (
                    <Box sx={{ display: 'flex', gap: 3, flexWrap: 'wrap' }}>
                      <Image src="/assets/images/landing/appstore.png" alt="App Store" width={150} height={45} />
                      <Image src="/assets/images/landing/playstoree.png" alt="Google Play" width={150} height={45} />
                    </Box>
                  ) : appIconType === 'ICON' ? (
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}>
                      <Typography variant="h5" sx={{ mb: 0 }}>
                        We are available on
                      </Typography>
                      <Image
                        src="/assets/images/footer/play-store.svg"
                        alt="Play Store"
                        width={40}
                        height={40}
                        style={{ objectFit: 'contain' }}
                      />
                      <Image
                        src="/assets/images/footer/apple-logo.svg"
                        alt="App Store"
                        width={40}
                        height={40}
                        style={{ objectFit: 'contain' }}
                      />
                    </Box>
                  ) : null)}
              </Box>
            </Grid>
          </Grid>
        </Container>
      </Box>
    </Box>
  );
}
