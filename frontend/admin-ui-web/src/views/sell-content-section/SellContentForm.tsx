'use client';

import {
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  DialogTitle,
  FormControl,
  FormControlLabel,
  FormLabel,
  Grid,
  IconButton,
  MenuItem,
  Radio,
  RadioGroup,
  Stack,
  Switch,
  TextField,
  Typography
} from '@mui/material';
import { Formik, Form, FieldArray } from 'formik';
import * as Yup from 'yup';
import {
  useGetSellContentSectionsQuery,
  useRemoveSellContentSectionMutation,
  useUpsertSellContentSectionMutation
} from 'redux/app/contents/sell-content/sellContentSectionApiSlice';
import { Add, Trash, ArrowUp, ArrowDown } from 'iconsax-react';
import { RenderType, SellSectionType } from 'enums/sell-content.enum';
import MainCard from 'components/MainCard';
import { v4 as uuidv4 } from 'uuid';
import dynamic from 'next/dynamic';
import { EditorProps } from 'react-draft-wysiwyg';
import { EditorState, convertToRaw, convertFromRaw } from 'draft-js';
import { useEffect, useState } from 'react';
import { fieldsExcludeMetaFields } from 'constants/shared';
import { useUploadFileMutation } from 'redux/app/contents/fileApiSlice';
import DeleteSellContentConfirmModal from './ConfirmSellContentDelete';
import 'react-draft-wysiwyg/dist/react-draft-wysiwyg.css';

const Editor = dynamic<EditorProps>(() => import('react-draft-wysiwyg').then((mod) => mod.Editor as unknown as React.FC<EditorProps>), {
  ssr: false
});

const itemSchema = Yup.object().shape({
  renderType: Yup.string().required('Render type is required'),

  title: Yup.string().when('renderType', {
    is: (val: string) => val !== RenderType.DESCRIPTION_ONLY && val !== RenderType.IMAGE_ONLY,
    then: (schema) => schema.required('Title is required'),
    otherwise: (schema) => schema.notRequired()
  }),

  description: Yup.string().when('renderType', {
    is: (val: string) => val === RenderType.TITLE_DESCRIPTION || val === RenderType.DESCRIPTION_ONLY,
    then: (schema) => schema.required('Description is required'),
    otherwise: (schema) => schema.notRequired()
  }),

  metadata: Yup.mixed().when('renderType', {
    is: (val: string) => val === RenderType.ACCORDION,
    then: () =>
      Yup.object().shape({
        subtitles: Yup.array()
          .min(1, 'At least one subtitle is required')
          .of(
            Yup.object().shape({
              subtitle: Yup.string().required('Subtitle is required'),
              description: Yup.string().required('Description is required')
            })
          )
      }),
    otherwise: () => Yup.mixed().notRequired()
  })
});

const sectionSchema = Yup.object().shape({
  sectionType: Yup.string().required(),
  sectionTitle: Yup.string().required(),
  displayOrder: Yup.number().min(1),
  items: Yup.array().min(1).of(itemSchema)
});

const validationSchema = Yup.object().shape({
  sections: Yup.array().of(sectionSchema).min(1)
});

const SellContentForm = () => {
  const [upsert] = useUpsertSellContentSectionMutation();
  const [removeSection] = useRemoveSellContentSectionMutation();
  const [uploadFile] = useUploadFileMutation();

  const {
    data: sectionsData,
    isLoading,
    refetch
  } = useGetSellContentSectionsQuery({
    where: {},
    order: ['displayOrder ASC'],
    include: [
      {
        relation: 'items',
        scope: {
          fields: fieldsExcludeMetaFields,
          order: ['displayOrder ASC']
        }
      }
    ],
    fields: fieldsExcludeMetaFields
  });

  const [editorStates, setEditorStates] = useState<Record<string, EditorState>>({});
  const [selectedImages, setSelectedImages] = useState<Record<string, File | null>>({});
  const [previewOpen, setPreviewOpen] = useState<boolean>(false);
  const [previewSrc, setPreviewSrc] = useState<string | null>(null);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [sectionToDelete, setSectionToDelete] = useState<{ index: number; id: string | null } | null>(null);

  const renderTypesWithoutImage = [RenderType.STEP, RenderType.BUTTON, RenderType.DESCRIPTION_ONLY];

  const initialValues = {
    sections:
      sectionsData?.map((s, sectionIndex) => ({
        ...s,
        id: s.id || uuidv4(),
        displayOrder: s.displayOrder || sectionIndex + 1,
        items: (s.items || [])
          .sort((a, b) => (a.displayOrder || 0) - (b.displayOrder || 0)) // Ensure items are sorted by displayOrder
          .map((item: any, itemIndex: number) => ({
            ...item,
            id: item.id || uuidv4(),
            displayOrder: item.displayOrder || itemIndex + 1,
            metadata:
              item.renderType === RenderType.ACCORDION
                ? { subtitles: item.metadata?.subtitles || [] }
                : item.renderType === RenderType.BUTTON
                  ? { redirectUrl: item.metadata?.redirectUrl || '' }
                  : {
                      showAppIcons: item.metadata?.showAppIcons ?? false,
                      appIconType: item.metadata?.appIconType ?? 'ICON'
                    }
          }))
      })) || []
  };

  useEffect(() => {
    const initialStates: Record<string, EditorState> = {};

    sectionsData?.forEach((section, si) => {
      section.items?.forEach((item, ii) => {
        const itemKey = `${si}-${ii}`;

        // Handle TITLE_DESCRIPTION items
        if (item.renderType === RenderType.TITLE_DESCRIPTION && item.description) {
          try {
            const content = convertFromRaw(JSON.parse(item.description));
            initialStates[itemKey] = EditorState.createWithContent(content);
          } catch {
            initialStates[itemKey] = EditorState.createEmpty();
          }
        }

        if (item.renderType === RenderType.DESCRIPTION_ONLY && item.description) {
          try {
            const content = convertFromRaw(JSON.parse(item.description));
            initialStates[itemKey] = EditorState.createWithContent(content);
          } catch {
            initialStates[itemKey] = EditorState.createEmpty();
          }
        }

        if (item.renderType === RenderType.STEP && item.description) {
          try {
            const content = convertFromRaw(JSON.parse(item.description));
            initialStates[itemKey] = EditorState.createWithContent(content);
          } catch {
            initialStates[itemKey] = EditorState.createEmpty();
          }
        }

        // Handle ACCORDION subtitles
        if (item.renderType === RenderType.ACCORDION && item.metadata?.subtitles?.length) {
          item.metadata.subtitles.forEach((sub: any, subi: number) => {
            const subKey = `${si}-${ii}-${subi}`;
            if (sub.description) {
              try {
                const content = convertFromRaw(JSON.parse(sub.description));
                initialStates[subKey] = EditorState.createWithContent(content);
              } catch {
                initialStates[subKey] = EditorState.createEmpty();
              }
            } else {
              initialStates[subKey] = EditorState.createEmpty();
            }
          });
        }
      });
    });

    setEditorStates(initialStates);
  }, [sectionsData]);

  return (
    <Formik
      enableReinitialize
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={async (values) => {
        // Upload all new images before saving
        for (let si = 0; si < values.sections.length; si++) {
          const section = values.sections[si];
          for (let ii = 0; ii < section.items.length; ii++) {
            const key = `${si}-${ii}`;
            const file = selectedImages[key];
            if (file) {
              const formData = new FormData();
              formData.append('file', file);
              const res = await uploadFile(formData).unwrap();
              values.sections[si].items[ii].imageUrl = res.key;
            }
          }
        }

        // Ensure proper ordering before submission
        const sectionsWithOrder = values.sections.map((section, sectionIndex) => ({
          ...section,
          displayOrder: sectionIndex + 1,
          items: section.items.map((item, itemIndex) => ({
            ...item,
            displayOrder: itemIndex + 1
          }))
        }));

        await upsert({ sections: sectionsWithOrder }).unwrap();
        refetch();
      }}
    >
      {({ values, handleChange, handleSubmit, setFieldValue }) => {
        const handleEditorChange = (key: string, state: EditorState, path: string) => {
          setEditorStates((prev) => ({ ...prev, [key]: state }));
          const raw = convertToRaw(state.getCurrentContent());
          setFieldValue(path, JSON.stringify(raw));
        };

        const handleConfirmDelete = async () => {
          if (sectionToDelete?.id) {
            await removeSection(sectionToDelete.id);
          }

          setFieldValue(
            'sections',
            values.sections.filter((_, i) => i !== sectionToDelete?.index)
          );
          setDeleteConfirmOpen(false);
          setSectionToDelete(null);
        };

        const allowedRenderTypesMap: Record<SellSectionType, RenderType[]> = {
          [SellSectionType.CARD_WITH_IMAGE]: [RenderType.BUTTON, RenderType.DESCRIPTION_ONLY, RenderType.IMAGE_ONLY],
          [SellSectionType.CARD_WITHOUT_IMAGE]: [RenderType.BUTTON, RenderType.DESCRIPTION_ONLY],
          [SellSectionType.FEATURE]: Object.values(RenderType),
          [SellSectionType.BENEFIT]: Object.values(RenderType),
          [SellSectionType.HOW_IT_WORKS]: [RenderType.STEP],
          [SellSectionType.DEFAULT]: [RenderType.DESCRIPTION_ONLY]
        };

        const getAvailableRenderTypes = (
          sectionType: SellSectionType,
          items: (typeof values.sections)[number]['items'],
          currentRenderType?: RenderType
        ): RenderType[] => {
          const allowed = allowedRenderTypesMap[sectionType] || Object.values(RenderType);
          const usedOnceOnly = [RenderType.DESCRIPTION_ONLY, RenderType.IMAGE_ONLY];

          return allowed.filter((rt) => {
            if (rt === currentRenderType) return true;
            if (usedOnceOnly.includes(rt)) {
              return !items.some((i) => i.renderType === rt);
            }
            return true;
          });
        };

        return (
          <>
            <Form onSubmit={handleSubmit}>
              <Grid container spacing={3}>
                {values.sections.map((section, si) => (
                  <Grid item xs={12} key={section.id || si}>
                    <MainCard
                      title={section.sectionTitle || `Section ${si + 1}`}
                      secondary={
                        <IconButton
                          color="error"
                          onClick={() => {
                            const isExisting = sectionsData?.some((s) => s.id === section.id);

                            if (isExisting && section.id) {
                              setSectionToDelete({ index: si, id: section.id });
                              setDeleteConfirmOpen(true);
                            } else {
                              const updated = [...values.sections];
                              updated.splice(si, 1);
                              setFieldValue('sections', updated);
                            }
                          }}
                        >
                          <Trash />
                        </IconButton>
                      }
                    >
                      <Grid container spacing={2}>
                        <Grid item xs={4}>
                          <TextField
                            select
                            fullWidth
                            label="Section Type"
                            name={`sections.${si}.sectionType`}
                            value={section.sectionType}
                            onChange={handleChange}
                          >
                            {Object.values(SellSectionType).map((type) => (
                              <MenuItem key={type} value={type}>
                                {type}
                              </MenuItem>
                            ))}
                          </TextField>
                        </Grid>
                        <Grid item xs={4}>
                          <TextField
                            fullWidth
                            label="Section Title"
                            name={`sections.${si}.sectionTitle`}
                            value={section.sectionTitle}
                            onChange={handleChange}
                          />
                        </Grid>
                        <Grid item xs={4}>
                          <TextField
                            fullWidth
                            type="number"
                            label="Display Order"
                            name={`sections.${si}.displayOrder`}
                            value={section.displayOrder}
                            onChange={handleChange}
                          />
                        </Grid>

                        <Grid item xs={12}>
                          <FieldArray name={`sections.${si}.items`}>
                            {(arrayHelpers) => (
                              <>
                                {section.items?.map((item, ii) => {
                                  const base = `sections.${si}.items.${ii}`;

                                  return (
                                    <Box key={item.id || ii} p={2} mb={2} border={1} borderColor="divider" borderRadius={2}>
                                      <Stack direction="row" justifyContent="space-between" alignItems="center" mb={2}>
                                        <Typography variant="subtitle1">Item {ii + 1}</Typography>
                                        <Stack direction="row" spacing={1}>
                                          {/* Move Up Button */}
                                          <IconButton
                                            size="small"
                                            disabled={ii === 0}
                                            onClick={() => {
                                              if (ii > 0) {
                                                arrayHelpers.swap(ii, ii - 1);
                                                // Update displayOrder after swap
                                                const updatedItems = [...section.items];
                                                [updatedItems[ii], updatedItems[ii - 1]] = [updatedItems[ii - 1], updatedItems[ii]];
                                                updatedItems.forEach((item, index) => {
                                                  item.displayOrder = index + 1;
                                                });
                                                setFieldValue(`sections.${si}.items`, updatedItems);
                                              }
                                            }}
                                          >
                                            <ArrowUp size={16} />
                                          </IconButton>
                                          {/* Move Down Button */}
                                          <IconButton
                                            size="small"
                                            disabled={ii === section.items.length - 1}
                                            onClick={() => {
                                              if (ii < section.items.length - 1) {
                                                arrayHelpers.swap(ii, ii + 1);
                                                // Update displayOrder after swap
                                                const updatedItems = [...section.items];
                                                [updatedItems[ii], updatedItems[ii + 1]] = [updatedItems[ii + 1], updatedItems[ii]];
                                                updatedItems.forEach((item, index) => {
                                                  item.displayOrder = index + 1;
                                                });
                                                setFieldValue(`sections.${si}.items`, updatedItems);
                                              }
                                            }}
                                          >
                                            <ArrowDown size={16} />
                                          </IconButton>
                                          {/* Delete Button */}
                                          <IconButton
                                            onClick={() => {
                                              arrayHelpers.remove(ii);
                                              // Update displayOrder for remaining items
                                              const updatedItems = section.items
                                                .filter((_, index) => index !== ii)
                                                .map((item, index) => ({ ...item, displayOrder: index + 1 }));
                                              setFieldValue(`sections.${si}.items`, updatedItems);
                                            }}
                                          >
                                            <Trash size={18} />
                                          </IconButton>
                                        </Stack>
                                      </Stack>
                                      <Grid container spacing={2}>
                                        <Grid item xs={4}>
                                          <TextField
                                            select
                                            fullWidth
                                            label="Render Type"
                                            name={`${base}.renderType`}
                                            value={item.renderType}
                                            onChange={handleChange}
                                          >
                                            {getAvailableRenderTypes(section.sectionType, section.items, item.renderType).map((rt) => (
                                              <MenuItem key={rt} value={rt}>
                                                {rt}
                                              </MenuItem>
                                            ))}
                                          </TextField>
                                        </Grid>
                                        {item.renderType !== RenderType.DESCRIPTION_ONLY && item.renderType !== RenderType.IMAGE_ONLY && (
                                          <Grid item xs={4}>
                                            <TextField
                                              fullWidth
                                              label="Title"
                                              name={`${base}.title`}
                                              value={item.title}
                                              onChange={handleChange}
                                            />
                                          </Grid>
                                        )}
                                        {!renderTypesWithoutImage.includes(item.renderType) && (
                                          <Grid item xs={4}>
                                            <Stack spacing={1}>
                                              <Button variant="outlined" component="label">
                                                {values.sections[si].items[ii].previewUrl ? 'Replace Image' : 'Upload Image'}
                                                <input
                                                  hidden
                                                  accept="image/*"
                                                  type="file"
                                                  onChange={(e) => {
                                                    const file = e.target.files?.[0] || null;
                                                    const key = `${si}-${ii}`;
                                                    setSelectedImages((prev) => ({ ...prev, [key]: file }));

                                                    // Clear any existing preview URL when new file is selected
                                                    if (file) {
                                                      setFieldValue(`${base}.previewUrl`, '');
                                                    }
                                                  }}
                                                />
                                              </Button>
                                              {selectedImages[`${si}-${ii}`] && (
                                                <Typography variant="body2" color="text.secondary">
                                                  {selectedImages[`${si}-${ii}`]?.name}
                                                </Typography>
                                              )}
                                              {values.sections[si].items[ii].previewUrl && (
                                                <>
                                                  <Button
                                                    variant="text"
                                                    size="small"
                                                    onClick={() => {
                                                      setPreviewSrc(values.sections[si].items[ii].previewUrl);
                                                      setPreviewOpen(true);
                                                    }}
                                                  >
                                                    Preview Image
                                                  </Button>
                                                </>
                                              )}
                                            </Stack>
                                          </Grid>
                                        )}
                                        {item.renderType === RenderType.TITLE_DESCRIPTION && (
                                          <Grid item xs={12}>
                                            <Box sx={{ p: 2, border: '1px solid #ccc', borderRadius: 2, minHeight: 300 }}>
                                              <Editor
                                                editorState={editorStates[`${si}-${ii}`] || EditorState.createEmpty()}
                                                onEditorStateChange={(state) =>
                                                  handleEditorChange(`${si}-${ii}`, state, `${base}.description`)
                                                }
                                                toolbarClassName="toolbar-class"
                                                wrapperClassName="wrapper-class"
                                                editorClassName="editor-class"
                                              />
                                            </Box>
                                          </Grid>
                                        )}

                                        {item.renderType === RenderType.ACCORDION && (
                                          <Grid item xs={12}>
                                            <FieldArray name={`${base}.metadata.subtitles`}>
                                              {(subsHelpers) => (
                                                <Box>
                                                  <Typography variant="subtitle2">Accordions</Typography>
                                                  {item.metadata?.subtitles?.map((sub: any, subi: number) => (
                                                    <Stack key={subi} direction="row" spacing={2} alignItems="center" mt={1} mb={1}>
                                                      <TextField
                                                        label="Subtitle"
                                                        name={`${base}.metadata.subtitles.${subi}.subtitle`}
                                                        value={sub.subtitle}
                                                        onChange={handleChange}
                                                        fullWidth
                                                      />
                                                      <Box sx={{ p: 2, border: '1px solid #ccc', borderRadius: 2, minHeight: 300 }}>
                                                        <Editor
                                                          editorState={editorStates[`${si}-${ii}-${subi}`] || EditorState.createEmpty()}
                                                          onEditorStateChange={(state) =>
                                                            handleEditorChange(
                                                              `${si}-${ii}-${subi}`,
                                                              state,
                                                              `${base}.metadata.subtitles.${subi}.description`
                                                            )
                                                          }
                                                          toolbarClassName="toolbar-class"
                                                          wrapperClassName="wrapper-class"
                                                          editorClassName="editor-class"
                                                        />
                                                      </Box>
                                                      <IconButton onClick={() => subsHelpers.remove(subi)}>
                                                        <Trash size={18} />
                                                      </IconButton>
                                                    </Stack>
                                                  ))}
                                                  <Button
                                                    size="small"
                                                    onClick={() => subsHelpers.push({ subtitle: '', description: '' })}
                                                    startIcon={<Add />}
                                                    sx={{ mt: 1 }}
                                                  >
                                                    Add Accordion
                                                  </Button>
                                                </Box>
                                              )}
                                            </FieldArray>
                                          </Grid>
                                        )}

                                        {(item.renderType === RenderType.STEP || item.renderType === RenderType.DESCRIPTION_ONLY) && (
                                          <Grid item xs={12}>
                                            {[SellSectionType.CARD_WITH_IMAGE, SellSectionType.CARD_WITHOUT_IMAGE].includes(
                                              section.sectionType
                                            ) && (
                                              <>
                                                <Grid item xs={12}>
                                                  <FormControlLabel
                                                    control={
                                                      <Switch
                                                        checked={item.metadata?.showAppIcons || false}
                                                        onChange={(e) => setFieldValue(`${base}.metadata.showAppIcons`, e.target.checked)}
                                                      />
                                                    }
                                                    label="Show Mobile App Icons"
                                                  />
                                                </Grid>

                                                {item.metadata?.showAppIcons && (
                                                  <Grid item xs={12}>
                                                    <FormControl component="fieldset">
                                                      <FormLabel component="legend">App Icon Type</FormLabel>
                                                      <RadioGroup
                                                        row
                                                        name={`${base}.metadata.appIconType`}
                                                        value={item.metadata?.appIconType || ''}
                                                        onChange={handleChange}
                                                      >
                                                        <FormControlLabel value="ICON" control={<Radio />} label="Icon Button" />
                                                        <FormControlLabel value="IMAGE" control={<Radio />} label="Image Button" />
                                                      </RadioGroup>
                                                    </FormControl>
                                                  </Grid>
                                                )}
                                              </>
                                            )}

                                            <Box sx={{ p: 2, border: '1px solid #ccc', borderRadius: 2, minHeight: 300 }}>
                                              <Editor
                                                editorState={editorStates[`${si}-${ii}`] || EditorState.createEmpty()}
                                                onEditorStateChange={(state) =>
                                                  handleEditorChange(`${si}-${ii}`, state, `${base}.description`)
                                                }
                                                toolbarClassName="toolbar-class"
                                                wrapperClassName="wrapper-class"
                                                editorClassName="editor-class"
                                              />
                                            </Box>
                                          </Grid>
                                        )}

                                        {item.renderType === RenderType.BUTTON && (
                                          <Grid item xs={12}>
                                            <TextField
                                              fullWidth
                                              label="Redirect URL"
                                              name={`${base}.metadata.redirectUrl`}
                                              value={item.metadata?.redirectUrl || ''}
                                              onChange={handleChange}
                                              placeholder="https://example.com"
                                            />
                                          </Grid>
                                        )}
                                      </Grid>
                                    </Box>
                                  );
                                })}

                                <Box display="flex" justifyContent="flex-end">
                                  <Button
                                    onClick={() => {
                                      const newItem = {
                                        id: uuidv4(),
                                        title: '',
                                        renderType: '',
                                        imageUrl: '',
                                        description: '',
                                        displayOrder: section.items.length + 1,
                                        metadata: {}
                                      };
                                      arrayHelpers.push(newItem);
                                    }}
                                    startIcon={<Add />}
                                    disabled={
                                      getAvailableRenderTypes(section.sectionType, section.items).length === 0 ||
                                      (section.sectionType === SellSectionType.DEFAULT && section.items.length >= 1)
                                    }
                                  >
                                    Add Item
                                  </Button>
                                </Box>
                              </>
                            )}
                          </FieldArray>
                        </Grid>
                      </Grid>
                    </MainCard>
                  </Grid>
                ))}

                <Grid item xs={12}>
                  <Box display="flex" justifyContent="flex-end" gap={2}>
                    <Button
                      variant="outlined"
                      startIcon={<Add />}
                      onClick={() =>
                        setFieldValue('sections', [
                          ...values.sections,
                          {
                            id: uuidv4(),
                            sectionType: '',
                            sectionTitle: '',
                            displayOrder: values.sections.length + 1,
                            items: []
                          }
                        ])
                      }
                    >
                      Add Section
                    </Button>
                    <Button
                      variant="contained"
                      color="primary"
                      type="submit"
                      disabled={isLoading}
                      startIcon={isLoading && <CircularProgress size={20} />}
                    >
                      {isLoading ? 'Saving...' : 'Save All'}
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </Form>
            <Dialog open={previewOpen} onClose={() => setPreviewOpen(false)} maxWidth="sm" fullWidth>
              <DialogTitle>Image Preview</DialogTitle>
              <DialogContent sx={{ textAlign: 'center' }}>
                {previewSrc && <img src={previewSrc} alt="Full Preview" style={{ maxWidth: '100%', height: 'auto', borderRadius: 8 }} />}
              </DialogContent>
            </Dialog>
            <DeleteSellContentConfirmModal
              open={deleteConfirmOpen}
              onClose={() => {
                setDeleteConfirmOpen(false);
                setSectionToDelete(null);
              }}
              onConfirm={handleConfirmDelete}
            />
          </>
        );
      }}
    </Formik>
  );
};

export default SellContentForm;
